{"version": 3, "file": "extension.test.js", "sourceRoot": "", "sources": ["../../../src/test/suite/extension.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,+CAAiC;AAGjC,KAAK,CAAC,sBAAsB,EAAE,GAAG,EAAE;IAC/B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;IAEzD,IAAI,CAAC,6BAA6B,EAAE,GAAG,EAAE;QACrC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,iCAAiC,CAAC,CAAC,CAAC;IACjF,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;QACzC,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,iCAAiC,CAAC,CAAC;QACpF,IAAI,SAAS,EAAE;YACX,MAAM,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC3B,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;SACjC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QAC7C,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAEzD,MAAM,gBAAgB,GAAG;YACrB,qBAAqB;YACrB,wBAAwB;YACxB,yBAAyB;YACzB,yBAAyB;YACzB,0BAA0B;YAC1B,kCAAkC;SACrC,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE;YACpC,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,WAAW,OAAO,uBAAuB,CAAC,CAAC;SACpF;IACL,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC"}